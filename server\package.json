{"name": "chat-backend", "version": "1.0.0", "main": "dist/index.js", "type": "module", "scripts": {"start": "node dist/index.js", "dev": "nodemon --exec node --loader ts-node/esm src/index.ts", "build": "tsc"}, "dependencies": {"express": "^4.18.2", "socket.io": "^4.7.2"}, "devDependencies": {"@types/express": "^4.17.17", "@types/node": "^20.5.7", "nodemon": "^3.0.1", "ts-node": "^10.9.1", "typescript": "^5.2.2"}}