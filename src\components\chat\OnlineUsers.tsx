import React from 'react';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Users } from 'lucide-react';

interface OnlineUsersProps {
  users: string[];
}

const OnlineUsers = ({ users }: OnlineUsersProps) => {
  return (
    <Card className="h-full border-0 bg-transparent shadow-none">
      <CardHeader className="pb-2">
        <CardTitle className="flex items-center text-lg">
          <Users className="h-5 w-5 mr-2 text-purple-500" />
          Online Users
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          {users.map((user, index) => (
            <div 
              key={index} 
              className="flex items-center p-2 rounded-lg bg-white border border-purple-100"
            >
              <div className="w-3 h-3 rounded-full bg-green-400 mr-2"></div>
              <span className="text-sm font-medium text-gray-700">{user}</span>
            </div>
          ))}
          {users.length === 0 && (
            <p className="text-sm text-gray-500 italic">No users online</p>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default OnlineUsers;