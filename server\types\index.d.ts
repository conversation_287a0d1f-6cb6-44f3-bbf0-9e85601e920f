import { Socket } from 'socket.io';

export interface User {
  id: string;
  username: string;
}

export interface Message {
  id: number;
  userId: string;
  username: string;
  text: string;
  timestamp: string;
}

export interface ServerToClientEvents {
  previousMessages: (messages: Message[]) => void;
  newMessage: (message: Message) => void;
  onlineUsers: (users: string[]) => void;
  userJoined: (username: string) => void;
  userLeft: (username: string) => void;
  userTyping: (data: { username: string; isTyping: boolean }) => void;
}

export interface ClientToServerEvents {
  join: (username: string) => void;
  sendMessage: (messageData: { text: string }) => void;
  typing: (isTyping: boolean) => void;
}

export type TypedSocket = Socket<ServerToClientEvents, ClientToServerEvents>;