import React, { useState, useEffect, useRef } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Send } from 'lucide-react';
import ChatMessage from './ChatMessage';
import OnlineUsers from './OnlineUsers';
import TypingIndicator from './TypingIndicator';
import { useChat } from '@/hooks/useChat';

const ChatContainer = () => {
  const [message, setMessage] = useState('');
  const { messages, onlineUsers, typingUsers, sendMessage, joinChat, userTyping } = useChat();
  const messagesEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const username = prompt('Enter your username:');
    if (username) {
      joinChat(username);
    }
  }, [joinChat]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSendMessage = (e: React.FormEvent) => {
    e.preventDefault();
    if (message.trim() && sendMessage) {
      sendMessage(message);
      setMessage('');
      userTyping(false);
    }
  };

  const handleTyping = (e: React.ChangeEvent<HTMLInputElement>) => {
    setMessage(e.target.value);
    userTyping(true);
    
    // Debounce typing indicator
    clearTimeout((window as any).typingTimeout);
    (window as any).typingTimeout = setTimeout(() => {
      userTyping(false);
    }, 1000);
  };

  return (
    <div className="flex h-screen bg-gradient-to-br from-purple-50 to-yellow-50 p-4">
      <div className="flex flex-col w-full max-w-4xl mx-auto">
        <Card className="flex-1 flex flex-col bg-white/80 backdrop-blur-sm border-0 shadow-xl rounded-2xl overflow-hidden">
          <div className="bg-gradient-to-r from-purple-400 to-green-300 p-4">
            <h1 className="text-2xl font-bold text-white text-center">Real-time Chat</h1>
          </div>
          
          <div className="flex-1 flex">
            <div className="flex-1 flex flex-col">
              <ScrollArea className="flex-1 p-4">
                <div className="space-y-4">
                  {messages.map((msg) => (
                    <ChatMessage key={msg.id} message={msg} />
                  ))}
                  <TypingIndicator typingUsers={typingUsers} />
                  <div ref={messagesEndRef} />
                </div>
              </ScrollArea>
              
              <form onSubmit={handleSendMessage} className="p-4 border-t border-gray-200">
                <div className="flex gap-2">
                  <Input
                    value={message}
                    onChange={handleTyping}
                    placeholder="Type your message..."
                    className="flex-1 rounded-full border-2 border-purple-200 focus:border-purple-400 focus:ring-purple-300"
                  />
                  <Button 
                    type="submit" 
                    className="rounded-full bg-gradient-to-r from-purple-500 to-green-400 hover:from-purple-600 hover:to-green-500"
                  >
                    <Send className="h-4 w-4" />
                  </Button>
                </div>
              </form>
            </div>
            
            <div className="w-64 border-l border-gray-200 bg-white/50">
              <OnlineUsers users={onlineUsers} />
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default ChatContainer;