import express from 'express'
import http from 'http'
import { Server } from 'socket.io'
import type { ServerToClientEvents, ClientToServerEvents, Message, User } from '../types/index.d.ts'

const app = express()
const server = http.createServer(app)
const io = new Server<ClientToServerEvents, ServerToClientEvents>(server, {
  cors: {
    origin: 'http://localhost:8080',
    methods: ['GET', 'POST']
  }
})

const PORT = process.env.PORT || 3001

// Store connected users and messages
const users: Record<string, string> = {}
const messages: Message[] = []

io.on('connection', socket => {
  console.log('User connected:', socket.id)

  // Handle user joining
  socket.on('join', username => {
    users[socket.id] = username
    socket.broadcast.emit('userJoined', username)
    io.emit('onlineUsers', Object.values(users))

    // Send previous messages to new user
    socket.emit('previousMessages', messages)
  })

  // Handle new messages
  socket.on('sendMessage', messageData => {
    const message: Message = {
      id: Date.now(),
      userId: socket.id,
      username: users[socket.id] || 'Anonymous',
      text: messageData.text,
      timestamp: new Date().toISOString()
    }

    messages.push(message)

    // Keep only last 100 messages
    if (messages.length > 100) {
      messages.splice(0, messages.length - 100)
    }

    io.emit('newMessage', message)
  })

  // Handle typing indicator
  socket.on('typing', isTyping => {
    const username = users[socket.id]
    if (username) {
      socket.broadcast.emit('userTyping', {
        username,
        isTyping
      })
    }
  })

  // Handle disconnection
  socket.on('disconnect', () => {
    const username = users[socket.id]
    delete users[socket.id]
    if (username) {
      io.emit('userLeft', username)
      console.log('User disconnected:', username)
    }
    io.emit('onlineUsers', Object.values(users))
  })
})

server.listen(PORT, () => {
  console.log(`Chat server running on port ${PORT}`)
})
