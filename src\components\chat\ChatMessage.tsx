import React from 'react';
import { Card } from '@/components/ui/card';
import { format } from 'date-fns';

interface Message {
  id: number;
  username: string;
  text: string;
  timestamp: string;
}

interface ChatMessageProps {
  message: Message;
}

const ChatMessage = ({ message }: ChatMessageProps) => {
  const isCurrentUser = message.username === localStorage.getItem('chatUsername');
  
  return (
    <div className={`flex ${isCurrentUser ? 'justify-end' : 'justify-start'}`}>
      <Card className={`max-w-xs md:max-w-md rounded-2xl p-4 ${
        isCurrentUser 
          ? 'bg-gradient-to-r from-green-200 to-yellow-200 rounded-br-none' 
          : 'bg-white rounded-bl-none border border-purple-100'
      }`}>
        <div className="flex items-center mb-1">
          <span className={`text-sm font-semibold ${
            isCurrentUser ? 'text-purple-700' : 'text-green-700'
          }`}>
            {message.username}
          </span>
          <span className="text-xs text-gray-500 ml-2">
            {format(new Date(message.timestamp), 'HH:mm')}
          </span>
        </div>
        <p className="text-gray-800">{message.text}</p>
      </Card>
    </div>
  );
};

export default ChatMessage;