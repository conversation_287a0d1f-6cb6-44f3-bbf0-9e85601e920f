import { useState, useEffect, useCallback } from 'react';
import { io, Socket } from 'socket.io-client';

const SOCKET_URL = 'http://localhost:3001';

interface Message {
  id: number;
  userId: string;
  username: string;
  text: string;
  timestamp: string;
}

export const useChat = () => {
  const [socket, setSocket] = useState<Socket | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [onlineUsers, setOnlineUsers] = useState<string[]>([]);
  const [typingUsers, setTypingUsers] = useState<string[]>([]);

  useEffect(() => {
    const newSocket = io(SOCKET_URL);
    setSocket(newSocket);

    newSocket.on('previousMessages', (msgs) => {
      setMessages(msgs);
    });

    newSocket.on('newMessage', (message) => {
      setMessages((prev) => [...prev, message]);
    });

    newSocket.on('onlineUsers', (users) => {
      setOnlineUsers(users);
    });

    newSocket.on('userJoined', (username) => {
      console.log(`${username} joined the chat`);
    });

    newSocket.on('userLeft', (username) => {
      console.log(`${username} left the chat`);
    });

    newSocket.on('userTyping', ({ username, isTyping }) => {
      setTypingUsers((prev) => {
        if (isTyping) {
          // Add user to typing list if not already there
          return prev.includes(username) ? prev : [...prev, username];
        } else {
          // Remove user from typing list
          return prev.filter((user) => user !== username);
        }
      });
    });

    return () => {
      newSocket.close();
    };
  }, []);

  const joinChat = useCallback((username: string) => {
    if (socket) {
      localStorage.setItem('chatUsername', username);
      socket.emit('join', username);
    }
  }, [socket]);

  const sendMessage = useCallback((text: string) => {
    if (socket) {
      socket.emit('sendMessage', { text });
    }
  }, [socket]);

  const userTyping = useCallback((isTyping: boolean) => {
    if (socket) {
      socket.emit('typing', isTyping);
    }
  }, [socket]);

  return {
    messages,
    onlineUsers,
    typingUsers,
    joinChat,
    sendMessage,
    userTyping
  };
};