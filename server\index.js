const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const path = require('path');

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: "http://localhost:8080",
    methods: ["GET", "POST"]
  }
});

const PORT = process.env.PORT || 3001;

// Store connected users and messages
let users = {};
let messages = [];

io.on('connection', (socket) => {
  console.log('User connected:', socket.id);

  // Handle user joining
  socket.on('join', (username) => {
    users[socket.id] = username;
    socket.broadcast.emit('userJoined', username);
    io.emit('onlineUsers', Object.values(users));
    
    // Send previous messages to new user
    socket.emit('previousMessages', messages);
  });

  // Handle new messages
  socket.on('sendMessage', (messageData) => {
    const message = {
      id: Date.now(),
      userId: socket.id,
      username: users[socket.id],
      text: messageData.text,
      timestamp: new Date().toISOString()
    };
    
    messages.push(message);
    
    // Keep only last 100 messages
    if (messages.length > 100) {
      messages = messages.slice(-100);
    }
    
    io.emit('newMessage', message);
  });

  // Handle typing indicator
  socket.on('typing', (isTyping) => {
    socket.broadcast.emit('userTyping', {
      username: users[socket.id],
      isTyping
    });
  });

  // Handle disconnection
  socket.on('disconnect', () => {
    const username = users[socket.id];
    delete users[socket.id];
    io.emit('userLeft', username);
    io.emit('onlineUsers', Object.values(users));
    console.log('User disconnected:', username);
  });
});

server.listen(PORT, () => {
  console.log(`Chat server running on port ${PORT}`);
});